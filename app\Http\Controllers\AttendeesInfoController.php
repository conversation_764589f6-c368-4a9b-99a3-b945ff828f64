<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AttendeesInfo;
use App\Models\Register;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AttendeesInfoController extends Controller
{
    public function uploadFiles(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'register_id' => 'required|exists:registers,id',
                'flight_file' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240', // 10MB
                'hotel_file' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240',
                'passport_file' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240',
            ]);

            $registerId = $request->register_id;

            // Get register to get event_id
            $register = Register::findOrFail($registerId);
            $eventId = $register->event_id;

            // Find or create attendees info record
            $attendeesInfo = AttendeesInfo::firstOrCreate(
                [
                    'event_id' => $eventId,
                    'register_id' => $registerId
                ],
                [
                    'flight_information' => null,
                    'hotel_information' => null,
                    'passport_details' => null,
                ]
            );

            // Handle file uploads
            $uploadedFiles = [];

            // Flight Information
            if ($request->hasFile('flight_file')) {
                $flightFile = $request->file('flight_file');
                $flightFileName = 'flight_' . $registerId . '_' . time() . '.' . $flightFile->getClientOriginalExtension();
                $flightPath = $flightFile->storeAs('public/attendees_files', $flightFileName);

                // Delete old file if exists
                if ($attendeesInfo->flight_information) {
                    Storage::delete('public/attendees_files/' . basename($attendeesInfo->flight_information));
                }

                $attendeesInfo->flight_information = $flightFileName;
                $uploadedFiles['flight'] = $flightFileName;
            }

            // Hotel Information
            if ($request->hasFile('hotel_file')) {
                $hotelFile = $request->file('hotel_file');
                $hotelFileName = 'hotel_' . $registerId . '_' . time() . '.' . $hotelFile->getClientOriginalExtension();
                $hotelPath = $hotelFile->storeAs('public/attendees_files', $hotelFileName);

                // Delete old file if exists
                if ($attendeesInfo->hotel_information) {
                    Storage::delete('public/attendees_files/' . basename($attendeesInfo->hotel_information));
                }

                $attendeesInfo->hotel_information = $hotelFileName;
                $uploadedFiles['hotel'] = $hotelFileName;
            }

            // Passport Details
            if ($request->hasFile('passport_file')) {
                $passportFile = $request->file('passport_file');
                $passportFileName = 'passport_' . $registerId . '_' . time() . '.' . $passportFile->getClientOriginalExtension();
                $passportPath = $passportFile->storeAs('public/attendees_files', $passportFileName);

                // Delete old file if exists
                if ($attendeesInfo->passport_details) {
                    Storage::delete('public/attendees_files/' . basename($attendeesInfo->passport_details));
                }

                $attendeesInfo->passport_details = $passportFileName;
                $uploadedFiles['passport'] = $passportFileName;
            }

            // Save the record
            $attendeesInfo->save();

            return response()->json([
                'success' => true,
                'message' => 'Files uploaded successfully!',
                'uploaded_files' => $uploadedFiles,
                'attendees_info' => $attendeesInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getFiles($register_id)
    {
        try {
            $attendeesInfo = AttendeesInfo::where('register_id', $register_id)->first();

            return response()->json([
                'success' => true,
                'attendees_info' => $attendeesInfo
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get files: ' . $e->getMessage()
            ], 500);
        }
    }

    public function downloadFile($register_id, $file_type)
    {
        try {
            $attendeesInfo = AttendeesInfo::where('register_id', $register_id)->first();

            if (!$attendeesInfo) {
                return response()->json(['error' => 'No documents found'], 404);
            }

            // Get file name based on type
            $fileName = null;
            switch ($file_type) {
                case 'flight':
                    $fileName = $attendeesInfo->flight_information;
                    break;
                case 'hotel':
                    $fileName = $attendeesInfo->hotel_information;
                    break;
                case 'passport':
                    $fileName = $attendeesInfo->passport_details;
                    break;
                default:
                    return response()->json(['error' => 'Invalid file type'], 400);
            }

            if (!$fileName) {
                return response()->json(['error' => 'File not found'], 404);
            }

            $filePath = storage_path('app/public/attendees_files/' . $fileName);

            if (!file_exists($filePath)) {
                return response()->json(['error' => 'File does not exist'], 404);
            }

            return response()->download($filePath, $fileName);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Download failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
